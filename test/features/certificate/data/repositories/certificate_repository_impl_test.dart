import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:selfeng/features/certificate/data/repositories/certificate_repository_impl.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';
import 'package:selfeng/services/firestore_service_service/domain/repositories/firestore_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';

import 'certificate_repository_impl_test.mocks.dart';

@GenerateMocks([
  FirestoreServiceRepository,
  FirebaseFirestore,
  FirebaseAuth,
  User,
  CollectionReference,
  DocumentReference,
  Query,
  QuerySnapshot,
  QueryDocumentSnapshot,
])
void main() {
  late CertificateRepositoryImpl repository;
  late MockFirestoreServiceRepository mockFirestoreService;
  late MockFirebaseFirestore mockFirestore;
  late MockFirebaseAuth mockAuth;
  late MockUser mockUser;
  late MockCollectionReference<Map<String, dynamic>> mockCollection;
  late MockDocumentReference<Map<String, dynamic>> mockDocument;
  late MockQuery<Map<String, dynamic>> mockQuery;
  late MockQuerySnapshot<Map<String, dynamic>> mockQuerySnapshot;
  late MockQueryDocumentSnapshot<Map<String, dynamic>> mockQueryDoc;

  setUp(() {
    mockFirestoreService = MockFirestoreServiceRepository();
    mockFirestore = MockFirebaseFirestore();
    mockAuth = MockFirebaseAuth();
    mockUser = MockUser();
    mockCollection = MockCollectionReference<Map<String, dynamic>>();
    mockDocument = MockDocumentReference<Map<String, dynamic>>();
    mockQuery = MockQuery<Map<String, dynamic>>();
    mockQuerySnapshot = MockQuerySnapshot<Map<String, dynamic>>();
    mockQueryDoc = MockQueryDocumentSnapshot<Map<String, dynamic>>();

    repository = CertificateRepositoryImpl(mockFirestoreService);

    // Setup default mocks
    when(mockFirestoreService.firebaseAuth).thenReturn(mockAuth);
    when(mockFirestoreService.fireStore).thenReturn(mockFirestore);
  });

  group('CertificateRepositoryImpl', () {
    group('getCertificates', () {
      test('should return Left with AppException when user is not authenticated', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = await repository.getCertificates();

        // Assert
        expect(result, isA<Left<AppException, List<Certificate>>>());
        result.fold(
          (error) {
            expect(error.statusCode, 401);
            expect(error.message, 'User not authenticated');
            expect(error.identifier, 'getCertificates');
          },
          (certificates) => fail('Should return error when user is not authenticated'),
        );
      });

      test('should return Right with empty list when no certificates exist', () async {
        // Arrange
        const userId = 'test-user-id';
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
        
        when(mockFirestore.collection('user-data')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.collection('certificates')).thenReturn(mockCollection);
        when(mockCollection.orderBy('generatedAt', descending: true)).thenReturn(mockQuery);
        when(mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([]);

        // Act
        final result = await repository.getCertificates();

        // Assert
        expect(result, isA<Right<AppException, List<Certificate>>>());
        result.fold(
          (error) => fail('Should return certificates list'),
          (certificates) => expect(certificates, isEmpty),
        );
      });

      test('should return Right with certificates when data exists', () async {
        // Arrange
        const userId = 'test-user-id';
        const docId = 'cert-1';
        final certificateData = {
          'certificateUrlPageOne': 'https://example.com/cert1.pdf',
          'certificateUrlPageTwo': 'https://example.com/cert1-page2.pdf',
          'level': 'A1',
          'generatedAt': '2023-12-01T10:00:00Z',
          'scores': {
            'pronunciation': 85.5,
            'listening': 90.0,
            'speaking': 88.2,
          },
          'predicates': {
            'pronunciation': 'Good',
            'listening': 'Excellent',
            'speaking': 'Very Good',
          },
        };

        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
        
        when(mockFirestore.collection('user-data')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.collection('certificates')).thenReturn(mockCollection);
        when(mockCollection.orderBy('generatedAt', descending: true)).thenReturn(mockQuery);
        when(mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([mockQueryDoc]);
        
        when(mockQueryDoc.id).thenReturn(docId);
        when(mockQueryDoc.data()).thenReturn(certificateData);

        // Act
        final result = await repository.getCertificates();

        // Assert
        expect(result, isA<Right<AppException, List<Certificate>>>());
        result.fold(
          (error) => fail('Should return certificates list'),
          (certificates) {
            expect(certificates, hasLength(1));
            final certificate = certificates.first;
            expect(certificate.id, docId);
            expect(certificate.certificateUrl, 'https://example.com/cert1.pdf');
            expect(certificate.certificateUrlPage2, 'https://example.com/cert1-page2.pdf');
            expect(certificate.level.name, 'A1');
            expect(certificate.title, 'Level A1 Certificate');
            expect(certificate.scores?.pronunciation, 85.5);
            expect(certificate.scores?.listening, 90.0);
            expect(certificate.scores?.speaking, 88.2);
            expect(certificate.predicates?.pronunciation, 'Good');
            expect(certificate.predicates?.listening, 'Excellent');
            expect(certificate.predicates?.speaking, 'Very Good');
          },
        );
      });

      test('should handle Firestore Timestamp for generatedAt', () async {
        // Arrange
        const userId = 'test-user-id';
        const docId = 'cert-1';
        final timestamp = Timestamp.fromDate(DateTime(2023, 12, 1, 10, 0, 0));
        final certificateData = {
          'certificateUrlPageOne': 'https://example.com/cert1.pdf',
          'level': 'B1',
          'generatedAt': timestamp,
        };

        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
        
        when(mockFirestore.collection('user-data')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.collection('certificates')).thenReturn(mockCollection);
        when(mockCollection.orderBy('generatedAt', descending: true)).thenReturn(mockQuery);
        when(mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([mockQueryDoc]);
        
        when(mockQueryDoc.id).thenReturn(docId);
        when(mockQueryDoc.data()).thenReturn(certificateData);

        // Act
        final result = await repository.getCertificates();

        // Assert
        expect(result, isA<Right<AppException, List<Certificate>>>());
        result.fold(
          (error) => fail('Should return certificates list'),
          (certificates) {
            expect(certificates, hasLength(1));
            final certificate = certificates.first;
            expect(certificate.dateIssued, DateTime(2023, 12, 1, 10, 0, 0));
          },
        );
      });

      test('should return Left with AppException when Firestore throws exception', () async {
        // Arrange
        const userId = 'test-user-id';
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
        
        when(mockFirestore.collection('user-data')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.collection('certificates')).thenReturn(mockCollection);
        when(mockCollection.orderBy('generatedAt', descending: true)).thenReturn(mockQuery);
        when(mockQuery.get()).thenThrow(Exception('Firestore error'));

        // Act
        final result = await repository.getCertificates();

        // Assert
        expect(result, isA<Left<AppException, List<Certificate>>>());
        result.fold(
          (error) {
            expect(error.statusCode, 0);
            expect(error.message, contains('Failed to fetch certificates'));
            expect(error.identifier, 'getCertificates');
          },
          (certificates) => fail('Should return error when Firestore throws exception'),
        );
      });

      test('should handle missing optional fields gracefully', () async {
        // Arrange
        const userId = 'test-user-id';
        const docId = 'cert-1';
        final certificateData = {
          'certificateUrlPageOne': 'https://example.com/cert1.pdf',
          'level': 'C1',
          'generatedAt': '2023-12-01T10:00:00Z',
          // Missing scores and predicates
        };

        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
        
        when(mockFirestore.collection('user-data')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.collection('certificates')).thenReturn(mockCollection);
        when(mockCollection.orderBy('generatedAt', descending: true)).thenReturn(mockQuery);
        when(mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([mockQueryDoc]);
        
        when(mockQueryDoc.id).thenReturn(docId);
        when(mockQueryDoc.data()).thenReturn(certificateData);

        // Act
        final result = await repository.getCertificates();

        // Assert
        expect(result, isA<Right<AppException, List<Certificate>>>());
        result.fold(
          (error) => fail('Should return certificates list'),
          (certificates) {
            expect(certificates, hasLength(1));
            final certificate = certificates.first;
            expect(certificate.scores, isNull);
            expect(certificate.predicates, isNull);
            expect(certificate.description, isEmpty);
          },
        );
      });
    });
  });
}
