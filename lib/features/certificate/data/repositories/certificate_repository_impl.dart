import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/entities/certificate.dart';
import '../../domain/repositories/certificate_repository.dart';
import '../models/certificate_model.dart';
import '../models/certificate_level_model.dart';

class CertificateRepositoryImpl implements CertificateRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  CertificateRepositoryImpl({FirebaseFirestore? firestore, FirebaseAuth? auth})
    : _firestore = firestore ?? FirebaseFirestore.instance,
      _auth = auth ?? FirebaseAuth.instance;

  @override
  Future<List<Certificate>> getCertificates() async {
    final user = _auth.currentUser;
    if (user == null) return [];

    final query =
        await _firestore
            .collection('user-data')
            .doc(user.uid)
            .collection('certificates')
            .orderBy('generatedAt', descending: true)
            .get();

    // Map Firestore docs to domain entities
    return query.docs.map((doc) {
      final data = doc.data();

      // Firestore structure specified by user
      final String? pageOne = data['certificateUrlPageOne'] as String?;
      final String? pageTwo = data['certificateUrlPageTwo'] as String?;
      final String levelStr = (data['level'] as String?) ?? '';

      // Support generatedAt as ISO string or Firestore Timestamp
      DateTime issuedAt = DateTime.fromMillisecondsSinceEpoch(0);
      final rawGeneratedAt = data['generatedAt'];
      if (rawGeneratedAt is String) {
        try {
          issuedAt = DateTime.parse(rawGeneratedAt);
        } catch (_) {}
      } else if (rawGeneratedAt is Timestamp) {
        issuedAt = rawGeneratedAt.toDate();
      }

      // Optional nested fields
      final Map<String, dynamic>? scores =
          (data['scores'] as Map<String, dynamic>?);
      final Map<String, dynamic>? predicates =
          (data['predicates'] as Map<String, dynamic>?);

      // Convert to the current domain model shape
      final levelModel = CertificateLevelModel(
        id: levelStr,
        name: levelStr.toUpperCase(),
        description: '',
      );

      // Build a title/description from available data for UI compatibility
      final title = 'Level $levelStr Certificate';
      final description = _buildDescription(scores, predicates);

      return CertificateModel(
        id: doc.id,
        title: title,
        description: description,
        dateIssued: issuedAt,
        certificateUrl: pageOne ?? '',
        certificateUrlPage2: pageTwo,
        level: levelModel,
      );
    }).toList();
  }

  String _buildDescription(
    Map<String, dynamic>? scores,
    Map<String, dynamic>? predicates,
  ) {
    final parts = <String>[];

    if (scores != null) {
      final pron = scores['pronunciation'];
      final list = scores['listening'];
      final speak = scores['speaking'];
      final scoresStr = [
        if (pron != null) 'Pronunciation: $pron',
        if (list != null) 'Listening: $list',
        if (speak != null) 'Speaking: $speak',
      ].join(' · ');
      if (scoresStr.isNotEmpty) parts.add(scoresStr);
    }

    if (predicates != null) {
      final pron = predicates['pronunciation'];
      final list = predicates['listening'];
      final speak = predicates['speaking'];
      final predsStr = [
        if (pron != null) 'Pronunciation: $pron',
        if (list != null) 'Listening: $list',
        if (speak != null) 'Speaking: $speak',
      ].join(' · ');
      if (predsStr.isNotEmpty) parts.add(predsStr);
    }

    return parts.isEmpty ? '' : parts.join(' | ');
  }
}
