import 'certificate_level_model.dart';
import '../../domain/entities/certificate.dart';

class CertificateModel extends Certificate {
  CertificateModel({
    required super.id,
    required super.title,
    required super.description,
    required super.dateIssued,
    required super.certificateUrl,
    super.certificateUrlPage2,
    required super.level,
  });

  factory CertificateModel.fromJson(Map<String, dynamic> json) {
    return CertificateModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      dateIssued: DateTime.parse(json['dateIssued']),
      certificateUrl: json['certificateUrl'],
      certificateUrlPage2: json['certificateUrlPage2'],
      level: CertificateLevelModel.fromJson(json['level']),
    );
  }
}
