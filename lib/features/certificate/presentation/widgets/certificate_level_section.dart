import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate.dart';
import 'package:selfeng/features/certificate/domain/entities/certificate_level.dart';
import 'package:selfeng/features/certificate/presentation/screens/certificate_screen.dart';

class CertificateLevelSection extends StatelessWidget {
  final CertificateLevel level;
  final List<Certificate> certificates;

  const CertificateLevelSection({
    super.key,
    required this.level,
    required this.certificates,
  });

  @override
  Widget build(BuildContext context) {
    final latestCertificate = certificates.first;
    final formattedDate = DateFormat(
      'd MMMM yyyy',
    ).format(latestCertificate.dateIssued);

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) =>
                    CertificateScreen(level: level, certificates: certificates),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16.0),
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              level.name,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 8),
            Text(
              '${certificates.length} certificates',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 4),
            Text(
              'Latest: $formattedDate',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            // In a real app, you would use CachedNetworkImage
            // For now, we use a placeholder
            Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8.0),
                image: DecorationImage(
                  // Placeholder image
                  image: NetworkImage(
                    'https://via.placeholder.com/300x150.png?text=Certificate+Preview',
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
