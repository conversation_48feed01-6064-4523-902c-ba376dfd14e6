import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../app/services/certificate_download_service.dart';
import '../../domain/entities/certificate.dart';
import '../../domain/entities/certificate_level.dart';

class CertificateScreen extends StatefulWidget {
  final CertificateLevel level;
  final List<Certificate> certificates;

  const CertificateScreen({
    super.key,
    required this.level,
    required this.certificates,
  });

  @override
  State<CertificateScreen> createState() => _CertificateScreenState();
}

class _CertificateScreenState extends State<CertificateScreen> {
  final CertificateDownloadService _downloadService =
      CertificateDownloadService();
  bool _isDownloading = false;

  Future<void> _download(String url, String title, String page) async {
    setState(() {
      _isDownloading = true;
    });

    final success = await _downloadService.downloadCertificate(
      url: url,
      fileName: '${title.replaceAll(' ', '_')}_$page.pdf',
    );

    setState(() {
      _isDownloading = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Download successful!' : 'Download failed.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.level.name)),
      body: Stack(
        children: [
          ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: widget.certificates.length,
            itemBuilder: (context, index) {
              final certificate = widget.certificates[index];
              final formattedDate = DateFormat(
                'd MMMM yyyy',
              ).format(certificate.dateIssued);

              return Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        certificate.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Issued on: $formattedDate',
                        style: const TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8.0),
                          image: DecorationImage(
                            image: NetworkImage(certificate.certificateUrl),
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton.icon(
                            onPressed:
                                _isDownloading
                                    ? null
                                    : () => _download(
                                      certificate.certificateUrl,
                                      certificate.title,
                                      'Page1',
                                    ),
                            icon: const Icon(Icons.download),
                            label: const Text('Page 1'),
                          ),
                          if (certificate.certificateUrlPage2 != null) ...[
                            const SizedBox(width: 8),
                            TextButton.icon(
                              onPressed:
                                  _isDownloading
                                      ? null
                                      : () => _download(
                                        certificate.certificateUrlPage2!,
                                        certificate.title,
                                        'Page2',
                                      ),
                              icon: const Icon(Icons.download),
                              label: const Text('Page 2'),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          if (_isDownloading) const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }
}
