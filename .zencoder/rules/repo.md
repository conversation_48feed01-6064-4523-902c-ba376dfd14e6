---
description: Repository Information Overview
alwaysApply: true
---

# SELF Mobile Application Information

## Summary
SELF Mobile is a Flutter-based mobile application for language learning. The app features various learning modules including main lessons, diagnostic tests, games, and questionnaires. It supports multiple platforms (Android, iOS) and integrates with Firebase services for authentication, storage, and messaging.

## Structure
- **lib/**: Core application code
  - **configs/**: App configuration and routes
  - **features/**: Feature modules (authentication, dashboard, games, lessons, etc.)
  - **main/**: App initialization and environment setup
  - **services/**: Service implementations (notifications, user data, etc.)
  - **shared/**: Shared utilities, widgets, and helpers
- **assets/**: Application resources (images, sounds, animations)
- **android/**, **ios/**, **web/**, **linux/**, **windows/**: Platform-specific code
- **test/**: Test files for the application

## Language & Runtime
**Language**: Dart
**Version**: SDK ^3.7.0
**Framework**: Flutter
**Package Manager**: pub (Flutter/Dart package manager)

## Dependencies
**Main Dependencies**:
- **State Management**: flutter_riverpod (^2.0.2), hooks_riverpod (^2.4.4)
- **Routing**: go_router (^16.0.0)
- **Networking**: dio (^5.2.1+1), http (^1.3.0)
- **Firebase**: firebase_core (^4.0.0), firebase_auth (^6.0.0), cloud_firestore (^6.0.0)
- **UI**: flutter_svg (^2.0.10+1), google_fonts (^6.2.1), lottie (^3.3.1)
- **Media**: flutter_sound (^9.8.1), audioplayers (^6.0.0), video_player (^2.9.2)

**Development Dependencies**:
- **Code Generation**: build_runner (^2.4.5), freezed (^3.1.0), json_serializable (^6.5.3)
- **Testing**: flutter_test, mocktail (^1.0.3), http_mock_adapter (^0.6.1)
- **Linting**: flutter_lints (^6.0.0), riverpod_lint (^2.3.2)

## Build & Installation
```bash
# Install dependencies
flutter pub get

# Run in development mode
flutter run --flavor dev -t lib/main/main_dev.dart

# Run in staging mode
flutter run --flavor staging -t lib/main/main_staging.dart

# Run in production mode
flutter run -t lib/main.dart

# Build for Android
flutter build apk --release

# Build for iOS
flutter build ios --release
```

## Environment Configuration
**Environment Files**: .env.dev, .env.stg, .env.prod
**Configuration Management**: Uses flutter_dotenv to load environment-specific variables
**Environments**: Development, Staging, Production

## Testing
**Framework**: flutter_test
**Test Location**: test/ directory
**Test Files**:
- Widget tests: test/widgets/
- Feature tests: test/features/
**Run Command**:
```bash
flutter test
```

## Internationalization
**Languages**: English (en), Indonesian (id)
**Configuration**: l10n.yaml
**Resource Files**: lib/l10n/app_en.arb, lib/l10n/app_id.arb
**Implementation**: Uses Flutter's built-in localization with intl package

## Firebase Integration
**Services**: Authentication, Firestore, Storage, Messaging, Functions
**Configuration**: Firebase options defined in firebase_options.dart
**Features**:
- User authentication (email, Google Sign-In)
- Cloud data storage
- Push notifications
- App Check for security